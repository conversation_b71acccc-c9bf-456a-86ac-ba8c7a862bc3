<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />

    <!-- PWA Meta Tags -->
    <meta name="application-name" content="JavaScript Full Course" />
    <meta name="description" content="Master JavaScript through interactive lessons, quizzes, and hands-on projects. Complete JavaScript course from basics to advanced concepts." />
    <meta name="keywords" content="JavaScript, programming, web development, coding, tutorial, course, interactive learning" />
    <meta name="author" content="JavaScript Full Course" />

    <!-- P<PERSON> Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Theme Colors -->
    <meta name="theme-color" content="#3b82f6" />
    <meta name="msapplication-TileColor" content="#3b82f6" />
    <meta name="msapplication-navbutton-color" content="#3b82f6" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="/icons/icon-192x192.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="144x144" href="/icons/icon-144x144.png" />
    <link rel="apple-touch-icon" sizes="120x120" href="/icons/icon-128x128.png" />

    <!-- Apple PWA Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-title" content="JS Course" />

    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileImage" content="/icons/icon-144x144.png" />
    <meta name="msapplication-config" content="/browserconfig.xml" />

    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-16x16.png" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="JavaScript Full Course - Interactive Learning Platform" />
    <meta property="og:description" content="Master JavaScript through interactive lessons, quizzes, and hands-on projects." />
    <meta property="og:image" content="/icons/icon-512x512.png" />
    <meta property="og:url" content="https://js-course.app" />
    <meta property="og:type" content="website" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="JavaScript Full Course" />
    <meta name="twitter:description" content="Master JavaScript through interactive lessons and projects." />
    <meta name="twitter:image" content="/icons/icon-512x512.png" />

    <title>JavaScript Full Course - Interactive Learning Platform</title>
  </head>
  <body>
    <div id="root"></div>

    <!-- PWA Install Prompt -->
    <div id="pwa-install-prompt" style="display: none; position: fixed; bottom: 20px; left: 20px; right: 20px; background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; padding: 16px; border-radius: 12px; box-shadow: 0 10px 40px rgba(0,0,0,0.2); z-index: 1000;">
      <div style="display: flex; align-items: center; justify-content: space-between;">
        <div>
          <h4 style="margin: 0 0 4px 0; font-weight: 600;">Install JavaScript Course</h4>
          <p style="margin: 0; font-size: 14px; opacity: 0.9;">Get the full app experience with offline access!</p>
        </div>
        <div style="display: flex; gap: 8px;">
          <button id="pwa-install-btn" style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 8px 16px; border-radius: 8px; cursor: pointer; font-weight: 500;">Install</button>
          <button id="pwa-dismiss-btn" style="background: transparent; border: 1px solid rgba(255,255,255,0.3); color: white; padding: 8px 12px; border-radius: 8px; cursor: pointer;">×</button>
        </div>
      </div>
    </div>

    <!-- Service Worker Registration -->
    <script>
      // PWA Installation
      let deferredPrompt;
      const installPrompt = document.getElementById('pwa-install-prompt');
      const installBtn = document.getElementById('pwa-install-btn');
      const dismissBtn = document.getElementById('pwa-dismiss-btn');

      // Listen for the beforeinstallprompt event
      window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;

        // Show install prompt if not already dismissed
        if (!localStorage.getItem('pwa-dismissed')) {
          installPrompt.style.display = 'block';
        }
      });

      // Handle install button click
      installBtn.addEventListener('click', async () => {
        if (deferredPrompt) {
          deferredPrompt.prompt();
          const { outcome } = await deferredPrompt.userChoice;
          console.log(`User response to the install prompt: ${outcome}`);
          deferredPrompt = null;
          installPrompt.style.display = 'none';
        }
      });

      // Handle dismiss button click
      dismissBtn.addEventListener('click', () => {
        installPrompt.style.display = 'none';
        localStorage.setItem('pwa-dismissed', 'true');
      });

      // Service Worker Registration
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', async () => {
          try {
            const registration = await navigator.serviceWorker.register('/sw.js');
            console.log('SW registered: ', registration);

            // Handle service worker updates
            registration.addEventListener('updatefound', () => {
              const newWorker = registration.installing;
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  // New content is available, show update notification
                  showUpdateNotification();
                }
              });
            });
          } catch (registrationError) {
            console.log('SW registration failed: ', registrationError);
          }
        });
      }

      // Show update notification
      function showUpdateNotification() {
        const updateBanner = document.createElement('div');
        updateBanner.innerHTML = `
          <div style="position: fixed; top: 20px; left: 20px; right: 20px; background: #10b981; color: white; padding: 16px; border-radius: 12px; box-shadow: 0 10px 40px rgba(0,0,0,0.2); z-index: 1001; display: flex; align-items: center; justify-content: space-between;">
            <div>
              <h4 style="margin: 0 0 4px 0; font-weight: 600;">Update Available</h4>
              <p style="margin: 0; font-size: 14px; opacity: 0.9;">A new version of the app is ready!</p>
            </div>
            <button onclick="window.location.reload()" style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 8px 16px; border-radius: 8px; cursor: pointer; font-weight: 500;">Update</button>
          </div>
        `;
        document.body.appendChild(updateBanner);

        // Auto-hide after 10 seconds
        setTimeout(() => {
          updateBanner.remove();
        }, 10000);
      }

      // Handle app installation
      window.addEventListener('appinstalled', () => {
        console.log('PWA was installed');
        installPrompt.style.display = 'none';

        // Show success message
        const successMsg = document.createElement('div');
        successMsg.innerHTML = `
          <div style="position: fixed; top: 20px; left: 20px; right: 20px; background: #10b981; color: white; padding: 16px; border-radius: 12px; box-shadow: 0 10px 40px rgba(0,0,0,0.2); z-index: 1001; text-align: center;">
            <h4 style="margin: 0 0 4px 0; font-weight: 600;">🎉 App Installed Successfully!</h4>
            <p style="margin: 0; font-size: 14px; opacity: 0.9;">You can now access JavaScript Course from your home screen.</p>
          </div>
        `;
        document.body.appendChild(successMsg);

        setTimeout(() => {
          successMsg.remove();
        }, 5000);
      });
    </script>

    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
