import { useState, useEffect } from 'react';
import { Wifi, WifiOff, Download, Smartphone } from 'lucide-react';

function PWAFeatures() {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showOfflineMessage, setShowOfflineMessage] = useState(false);
  const [deferredPrompt, setDeferredPrompt] = useState(null);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);

  useEffect(() => {
    // Online/Offline status
    const handleOnline = () => {
      setIsOnline(true);
      setShowOfflineMessage(false);
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowOfflineMessage(true);
      setTimeout(() => setShowOfflineMessage(false), 5000);
    };

    // PWA Install prompt
    const handleBeforeInstallPrompt = (e) => {
      e.preventDefault();
      setDeferredPrompt(e);
      
      // Show install prompt if not already dismissed
      if (!localStorage.getItem('pwa-install-dismissed')) {
        setShowInstallPrompt(true);
      }
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    // Check if app is already installed
    if (window.matchMedia('(display-mode: standalone)').matches) {
      setShowInstallPrompt(false);
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  const handleInstallClick = async () => {
    if (deferredPrompt) {
      deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('User accepted the install prompt');
      } else {
        console.log('User dismissed the install prompt');
      }
      
      setDeferredPrompt(null);
      setShowInstallPrompt(false);
    }
  };

  const handleDismissInstall = () => {
    setShowInstallPrompt(false);
    localStorage.setItem('pwa-install-dismissed', 'true');
  };

  return (
    <>
      {/* Offline Indicator */}
      {showOfflineMessage && (
        <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 animate-slideUp">
          <div className="bg-red-500 text-white px-4 py-2 rounded-full shadow-lg flex items-center space-x-2">
            <WifiOff className="h-4 w-4" />
            <span className="text-sm font-medium">You're offline</span>
          </div>
        </div>
      )}

      {/* Online Indicator */}
      {!isOnline && (
        <div className="fixed top-4 right-4 z-50">
          <div className="bg-gray-800 text-white px-3 py-2 rounded-lg shadow-lg flex items-center space-x-2">
            <WifiOff className="h-4 w-4 text-red-400" />
            <span className="text-sm">Offline Mode</span>
          </div>
        </div>
      )}

      {/* PWA Install Prompt */}
      {showInstallPrompt && (
        <div className="fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:w-96 z-50 animate-slideUp">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-xl shadow-2xl border border-white/20">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-white/20 rounded-lg">
                <Smartphone className="h-6 w-6" />
              </div>
              <div className="flex-1">
                <h4 className="font-semibold mb-1">Install JavaScript Course</h4>
                <p className="text-sm text-white/90 mb-3">
                  Get the full app experience with offline access and faster loading!
                </p>
                <div className="flex space-x-2">
                  <button
                    onClick={handleInstallClick}
                    className="flex items-center space-x-2 bg-white/20 hover:bg-white/30 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                  >
                    <Download className="h-4 w-4" />
                    <span>Install</span>
                  </button>
                  <button
                    onClick={handleDismissInstall}
                    className="px-3 py-2 text-sm text-white/80 hover:text-white transition-colors duration-200"
                  >
                    Not now
                  </button>
                </div>
              </div>
              <button
                onClick={handleDismissInstall}
                className="text-white/60 hover:text-white transition-colors duration-200"
              >
                ×
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Network Status Indicator (always visible in corner) */}
      <div className="fixed top-4 left-4 z-40">
        <div className={`p-2 rounded-full shadow-lg transition-all duration-300 ${
          isOnline 
            ? 'bg-green-500 text-white' 
            : 'bg-red-500 text-white animate-pulse'
        }`}>
          {isOnline ? (
            <Wifi className="h-4 w-4" />
          ) : (
            <WifiOff className="h-4 w-4" />
          )}
        </div>
      </div>
    </>
  );
}

export default PWAFeatures;
