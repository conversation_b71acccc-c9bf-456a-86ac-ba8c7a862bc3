import { useAppContext } from '../../contexts/AppContext';
import { Menu, Sun, Moon, Download, Smartphone } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useState, useEffect } from 'react';

function Navbar({ toggleSidebar }) {
  const { darkMode, toggleDarkMode } = useAppContext();
  const [deferredPrompt, setDeferredPrompt] = useState(null);
  const [showInstallButton, setShowInstallButton] = useState(false);

  useEffect(() => {
    const handleBeforeInstallPrompt = (e) => {
      console.log('PWA install prompt available');
      e.preventDefault();
      setDeferredPrompt(e);
      setShowInstallButton(true);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    // Check if app is already installed
    if (window.matchMedia('(display-mode: standalone)').matches) {
      console.log('App is already installed');
      setShowInstallButton(false);
    } else {
      console.log('App is not installed, checking for PWA support...');
    }

    // Debug: Check if service worker is registered
    if ('serviceWorker' in navigator) {
      console.log('Service Worker supported');
    } else {
      console.log('Service Worker not supported');
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  const handleInstallClick = async () => {
    if (deferredPrompt) {
      deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;

      if (outcome === 'accepted') {
        console.log('User accepted the install prompt');
      }

      setDeferredPrompt(null);
      setShowInstallButton(false);
    } else {
      // Fallback: Show instructions for manual installation
      alert('To install this app:\n\n1. Open browser menu (⋮)\n2. Select "Install app" or "Add to Home Screen"\n3. Follow the prompts\n\nNote: PWA installation requires HTTPS or localhost.');
    }
  };

  return (
    <header className="sticky top-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200/50 dark:border-gray-700/50 shadow-soft">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Left side - Logo and mobile menu button */}
          <div className="flex items-center">
            <button
              onClick={toggleSidebar}
              className="inline-flex items-center justify-center p-2.5 rounded-xl text-gray-500 hover:text-gray-700 hover:bg-gray-100/80 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-800/80 focus:outline-none focus:ring-2 focus:ring-primary-500 lg:hidden transition-all duration-200 hover:scale-105"
              aria-label="Open sidebar"
            >
              <Menu className="h-5 w-5" />
            </button>

            <Link to="/" className="flex items-center ml-3 lg:ml-0 group">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-primary-600 to-accent-600 rounded-xl blur opacity-20 group-hover:opacity-40 transition-opacity duration-300"></div>
                <div className="relative px-4 py-2 bg-gradient-to-r from-primary-600 to-accent-600 rounded-xl">
                  <span className="text-xl font-bold text-white">LearnHub</span>
                </div>
              </div>
            </Link>
          </div>

          {/* Right side - PWA Install and Theme toggle */}
          <div className="flex items-center space-x-2">
            {/* PWA Install Button - Always visible for testing */}
            {(showInstallButton || true) && (
              <button
                onClick={handleInstallClick}
                className="relative inline-flex items-center justify-center p-2.5 rounded-xl text-gray-500 hover:text-gray-700 hover:bg-gray-100/80 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-800/80 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-all duration-200 hover:scale-105 group"
                aria-label="Install App"
              >
                <div className="relative">
                  <Download className="h-5 w-5 transform group-hover:scale-110 transition-transform duration-200" />
                </div>

                {/* Tooltip */}
                <div className="absolute -bottom-10 right-0 px-2 py-1 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                  Install App
                </div>
              </button>
            )}

            {/* Theme Toggle */}
            <button
              onClick={toggleDarkMode}
              className="relative inline-flex items-center justify-center p-2.5 rounded-xl text-gray-500 hover:text-gray-700 hover:bg-gray-100/80 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-800/80 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-all duration-200 hover:scale-105 group"
              aria-label={darkMode ? 'Switch to light mode' : 'Switch to dark mode'}
            >
              <div className="relative">
                {darkMode ? (
                  <Sun className="h-5 w-5 transform group-hover:rotate-12 transition-transform duration-200" />
                ) : (
                  <Moon className="h-5 w-5 transform group-hover:-rotate-12 transition-transform duration-200" />
                )}
              </div>

              {/* Tooltip */}
              <div className="absolute -bottom-10 right-0 px-2 py-1 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                {darkMode ? 'Light mode' : 'Dark mode'}
              </div>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
}

export default Navbar;